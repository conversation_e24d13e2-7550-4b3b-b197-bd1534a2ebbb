#!/usr/bin/env python3
"""
MaixCAM 串口通信示例代码
只发送0x01到STM32
"""

from maix import uart, time, app

def main():
    # 初始化串口，使用默认串口设备
    # 波特率115200，8位数据位，1位停止位，无校验
    serial = uart.UART("/dev/ttyS0", 115200)
    
    print("MaixCAM UART 通信开始...")
    print("持续发送0x01到STM32...")
    
    while not app.need_exit():
        try:
            # 发送单个字节 0x01
            data = bytearray([0x01])
            serial.write(data)
            
            print("发送: 0x01")
            
            # 每200ms发送一次
            time.sleep(0.2)
            
        except Exception as e:
            print(f"发送错误: {e}")
            time.sleep(1)
    
    # 关闭串口
    serial.close()
    print("串口通信结束")

if __name__ == "__main__":
    main()
